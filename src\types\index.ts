export interface LinkData {
  id: string;
  url: string;
  title: string;
  description?: string;
  image: string;
  platform: Platform;
  dateAdded: string;
  tags: string[];
  category?: string;
  isBookmarked: boolean;
  clickCount: number;
  lastAccessed?: string;
  metadata?: LinkMetadata;
}

export interface LinkMetadata {
  author?: string;
  publishDate?: string;
  duration?: string;
  views?: number;
  likes?: number;
  channelName?: string;
  thumbnailUrl?: string;
  embedUrl?: string;
}

export type Platform = 
  | 'YouTube'
  | 'Telegram'
  | 'Twitter'
  | 'Instagram'
  | 'TikTok'
  | 'LinkedIn'
  | 'Reddit'
  | 'Facebook'
  | 'WhatsApp'
  | 'Discord'
  | 'Twitch'
  | 'GitHub'
  | 'Medium'
  | 'Pinterest'
  | 'Snapchat'
  | 'Vimeo'
  | 'Dailymotion'
  | 'SoundCloud'
  | 'Spotify'
  | 'رابط عام';

export interface Collection {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  linkIds: string[];
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
}

export interface FilterOptions {
  platform?: Platform | 'all';
  category?: string;
  tags?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  searchQuery?: string;
  isBookmarked?: boolean;
  sortBy?: 'dateAdded' | 'title' | 'platform' | 'clickCount' | 'lastAccessed';
  sortOrder?: 'asc' | 'desc';
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  language: 'ar' | 'en';
  defaultView: 'grid' | 'list' | 'timeline';
  itemsPerPage: number;
  autoExtractMetadata: boolean;
  showNotifications: boolean;
  exportFormat: 'csv' | 'json' | 'excel';
  backupEnabled: boolean;
  syncEnabled: boolean;
}

export interface Analytics {
  totalLinks: number;
  totalClicks: number;
  platformDistribution: Record<Platform, number>;
  dailyActivity: Array<{
    date: string;
    linksAdded: number;
    clicks: number;
  }>;
  topPlatforms: Array<{
    platform: Platform;
    count: number;
    percentage: number;
  }>;
  recentActivity: Array<{
    type: 'add' | 'click' | 'bookmark' | 'delete';
    linkId: string;
    timestamp: string;
  }>;
}

export interface ExportData {
  links: LinkData[];
  collections: Collection[];
  settings: AppSettings;
  exportDate: string;
  version: string;
}

export interface ImportResult {
  success: boolean;
  importedLinks: number;
  importedCollections: number;
  errors: string[];
  duplicates: number;
}

export interface NotificationData {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface ViewMode {
  type: 'grid' | 'list' | 'timeline' | 'kanban';
  columns?: number;
  showImages?: boolean;
  showMetadata?: boolean;
  compactMode?: boolean;
}
