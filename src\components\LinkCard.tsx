'use client';

import React, { useState } from 'react';
import { LinkData } from '@/types';
import { useLinksStore } from '@/stores/useLinksStore';
import { 
  Heart, 
  ExternalLink, 
  Trash2, 
  Copy, 
  Share2, 
  Tag,
  Calendar,
  Eye,
  MoreVertical,
  Bookmark,
  BookmarkCheck
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';

interface LinkCardProps {
  link: LinkData;
  isSelected?: boolean;
  onSelect?: (id: string) => void;
  viewMode?: 'grid' | 'list';
}

export function LinkCard({ link, isSelected, onSelect, viewMode = 'grid' }: LinkCardProps) {
  const [imageError, setImageError] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const { 
    deleteLink, 
    toggleBookmark, 
    incrementClickCount,
    addNotification 
  } = useLinksStore();

  const handleLinkClick = () => {
    incrementClickCount(link.id);
    window.open(link.url, '_blank', 'noopener,noreferrer');
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    deleteLink(link.id);
    addNotification({
      type: 'success',
      title: 'تم الحذف',
      message: 'تم حذف الرابط بنجاح'
    });
  };

  const handleBookmark = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleBookmark(link.id);
    addNotification({
      type: 'success',
      title: link.isBookmarked ? 'تم إلغاء الإشارة المرجعية' : 'تم إضافة إشارة مرجعية',
      message: link.isBookmarked ? 'تم إزالة الرابط من المفضلة' : 'تم إضافة الرابط إلى المفضلة'
    });
  };

  const handleCopyLink = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(link.url);
      addNotification({
        type: 'success',
        title: 'تم النسخ',
        message: 'تم نسخ الرابط إلى الحافظة'
      });
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'فشل النسخ',
        message: 'لم يتم نسخ الرابط'
      });
    }
  };

  const handleShare = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (navigator.share) {
      try {
        await navigator.share({
          title: link.title,
          url: link.url,
        });
      } catch (error) {
        console.log('مشاركة ملغاة');
      }
    } else {
      handleCopyLink(e);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: ar 
      });
    } catch {
      return 'تاريخ غير صحيح';
    }
  };

  if (viewMode === 'list') {
    return (
      <div 
        className={`
          flex items-center gap-4 p-4 bg-card rounded-lg border transition-all duration-200
          hover:shadow-md hover:scale-[1.01] cursor-pointer
          ${isSelected ? 'ring-2 ring-primary border-primary' : ''}
        `}
        onClick={() => onSelect?.(link.id)}
      >
        {/* Image */}
        <div className="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden bg-muted">
          {!imageError ? (
            <img
              src={link.image}
              alt={link.platform}
              className="w-full h-full object-cover"
              onError={() => setImageError(true)}
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <ExternalLink size={24} />
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-foreground truncate text-right">
                {link.title}
              </h3>
              <p className="text-sm text-muted-foreground truncate text-right mt-1">
                {link.url}
              </p>
              {link.description && (
                <p className="text-sm text-muted-foreground line-clamp-2 text-right mt-1">
                  {link.description}
                </p>
              )}
            </div>
            
            {/* Platform Badge */}
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary whitespace-nowrap">
              {link.platform}
            </span>
          </div>

          {/* Metadata */}
          <div className="flex items-center justify-between mt-3 text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <Calendar size={12} />
                {formatDate(link.dateAdded)}
              </span>
              {link.clickCount > 0 && (
                <span className="flex items-center gap-1">
                  <Eye size={12} />
                  {link.clickCount}
                </span>
              )}
              {link.tags.length > 0 && (
                <span className="flex items-center gap-1">
                  <Tag size={12} />
                  {link.tags.length}
                </span>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-1">
              <button
                onClick={handleBookmark}
                className="p-1 rounded hover:bg-muted transition-colors"
                title={link.isBookmarked ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
              >
                {link.isBookmarked ? (
                  <BookmarkCheck size={14} className="text-yellow-500" />
                ) : (
                  <Bookmark size={14} />
                )}
              </button>
              
              <button
                onClick={handleCopyLink}
                className="p-1 rounded hover:bg-muted transition-colors"
                title="نسخ الرابط"
              >
                <Copy size={14} />
              </button>
              
              <button
                onClick={handleShare}
                className="p-1 rounded hover:bg-muted transition-colors"
                title="مشاركة"
              >
                <Share2 size={14} />
              </button>
              
              <button
                onClick={handleLinkClick}
                className="p-1 rounded hover:bg-muted transition-colors"
                title="فتح الرابط"
              >
                <ExternalLink size={14} />
              </button>
              
              <button
                onClick={handleDelete}
                className="p-1 rounded hover:bg-destructive/10 hover:text-destructive transition-colors"
                title="حذف"
              >
                <Trash2 size={14} />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid view
  return (
    <div 
      className={`
        group relative bg-card rounded-xl border overflow-hidden transition-all duration-300
        hover:shadow-lg hover:scale-[1.02] cursor-pointer animate-fade-in
        ${isSelected ? 'ring-2 ring-primary border-primary' : ''}
      `}
      onClick={() => onSelect?.(link.id)}
    >
      {/* Image */}
      <div className="relative aspect-video bg-muted overflow-hidden">
        {!imageError ? (
          <img
            src={link.image}
            alt={link.platform}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            onError={() => setImageError(true)}
            loading="lazy"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-muted-foreground">
            <ExternalLink size={48} />
          </div>
        )}
        
        {/* Platform Badge */}
        <div className="absolute top-2 right-2">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-black/70 text-white backdrop-blur-sm">
            {link.platform}
          </span>
        </div>

        {/* Bookmark Icon */}
        {link.isBookmarked && (
          <div className="absolute top-2 left-2">
            <BookmarkCheck size={20} className="text-yellow-400 drop-shadow-lg" />
          </div>
        )}

        {/* Quick Actions Overlay */}
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
          <button
            onClick={handleLinkClick}
            className="p-2 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-colors"
            title="فتح الرابط"
          >
            <ExternalLink size={20} className="text-white" />
          </button>
          
          <button
            onClick={handleCopyLink}
            className="p-2 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-colors"
            title="نسخ الرابط"
          >
            <Copy size={20} className="text-white" />
          </button>
          
          <button
            onClick={handleShare}
            className="p-2 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-colors"
            title="مشاركة"
          >
            <Share2 size={20} className="text-white" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <h3 className="font-semibold text-foreground line-clamp-2 text-right mb-2 min-h-[2.5rem]">
          {link.title}
        </h3>
        
        {link.description && (
          <p className="text-sm text-muted-foreground line-clamp-2 text-right mb-3">
            {link.description}
          </p>
        )}

        {/* Tags */}
        {link.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {link.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-secondary text-secondary-foreground"
              >
                {tag}
              </span>
            ))}
            {link.tags.length > 3 && (
              <span className="text-xs text-muted-foreground">
                +{link.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-3">
            <span className="flex items-center gap-1">
              <Calendar size={12} />
              {formatDate(link.dateAdded)}
            </span>
            {link.clickCount > 0 && (
              <span className="flex items-center gap-1">
                <Eye size={12} />
                {link.clickCount}
              </span>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-1">
            <button
              onClick={handleBookmark}
              className="p-1 rounded hover:bg-muted transition-colors"
              title={link.isBookmarked ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
            >
              {link.isBookmarked ? (
                <BookmarkCheck size={14} className="text-yellow-500" />
              ) : (
                <Bookmark size={14} />
              )}
            </button>
            
            <button
              onClick={handleDelete}
              className="p-1 rounded hover:bg-destructive/10 hover:text-destructive transition-colors"
              title="حذف"
            >
              <Trash2 size={14} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
