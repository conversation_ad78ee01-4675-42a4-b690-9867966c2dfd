<!DOCTYPE html>
<html lang="ar" >
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>عارض الروابط الذكي</title>
  <style>
    body {
      font-family: 'Segoe UI', sans-serif;
      margin: 0;
      padding: 2rem;
      direction: rtl;
      background: #f5f7fa;
      color: #222;
      transition: background 0.3s ease;
    }
    @media (prefers-color-scheme: dark) {
      body {
        background: #121212;
        color: #eee;
      }
      .card {
        background: #1e1e1e;
        color: #ddd;
      }
      select, textarea, button {
        background: #222;
        color: #eee;
        border: 1px solid #555;
      }
    }
    h1 {
      text-align: center;
      margin-bottom: 2rem;
    }
    .input-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;
    }
    textarea {
      width: 80%;
      padding: 12px;
      border: 1px solid #ccc;
      border-radius: 8px;
      resize: vertical;
      font-size: 1rem;
    }
    select {
      padding: 8px;
      border-radius: 6px;
      border: 1px solid #ccc;
      font-size: 1rem;
      width: 200px;
      margin-bottom: 1rem;
      cursor: pointer;
    }
    button {
      padding: 10px 20px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      transition: background 0.2s ease;
    }
    button:hover {
      background: #0056b3;
    }
    .cards {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 1.5rem;
    }
    .card {
      width: 320px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      padding: 1rem;
      text-align: center;
      position: relative;
      transition: transform 0.3s;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .card:hover {
      transform: scale(1.03);
    }
    .card img {
      width: 100%;
      height: 160px;
      object-fit: cover;
      border-radius: 8px;
      margin-bottom: 1rem;
      user-select: none;
    }
    .platform {
      font-weight: bold;
      color: #007bff;
      margin-bottom: 0.3rem;
    }
    .delete-btn {
      position: absolute;
      top: 10px;
      left: 10px;
      background: crimson;
      border: none;
      color: white;
      border-radius: 50%;
      width: 28px;
      height: 28px;
      cursor: pointer;
      font-weight: bold;
      line-height: 28px;
    }
    .date-added {
      font-size: 0.8rem;
      color: #666;
      margin-top: auto;
      user-select: none;
    }
    a {
      word-break: break-word;
      color: #007bff;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>

  <h1>عارض الروابط الذكي</h1>

  <div class="input-container">
    <textarea id="linkInput" rows="3" placeholder="الصق رابطًا أو عدة روابط..."></textarea>
    <button onclick="processLinks()">تحليل الروابط</button>

    <select id="filterPlatform" onchange="filterCards()">
      <option value="all">جميع المنصات</option>
      <option value="YouTube">يوتيوب</option>
      <option value="Telegram">تلجرام</option>
      <option value="رابط عام">رابط عام</option>
    </select>

    <button onclick="exportCSV()">تصدير إلى CSV</button>
  </div>

  <div class="cards" id="cardsContainer"></div>

<script>
  const storageKey = 'smartLinkCards';

  window.onload = () => {
    const saved = JSON.parse(localStorage.getItem(storageKey)) || [];
    saved.forEach(data => createCard(data.url, data.platform, data.title, data.image, data.date));
  };

  function detectPlatform(url) {
    if (url.includes("youtube.com") || url.includes("youtu.be")) return "YouTube";
    if (url.includes("t.me")) return "Telegram";
    return "رابط عام";
  }

  function getImageForPlatform(platform, telegramChannel=null) {
    switch (platform) {
      case "YouTube": return "https://img.icons8.com/color/480/youtube-play.png";
      case "Telegram": 
        // نستخدم صورة قناة تلجرام إذا متوفرة (سيتم تمرير telegramChannel)
        if (telegramChannel) {
          // غالباً لا نستطيع جلب صورة القناة الأصلية بسبب CORS لذلك نستخدم أيقونة عامة مع اسم القناة
          return `https://avatars.telegram.org/${telegramChannel}.jpg`; // هذا رابط افتراضي (غير حقيقي) لكن نحتفظ كتعليمات
        }
        return "https://img.icons8.com/color/480/telegram-app.png";
      default: return "https://img.icons8.com/fluency/480/link.png";
    }
  }

  async function processLinks() {
    const input = document.getElementById("linkInput");
    const urls = input.value.split(/\n|,/).map(link => link.trim()).filter(Boolean);
    for (const url of urls) {
      await handleLink(url);
    }
    input.value = "";
  }

  async function handleLink(url) {
    const platform = detectPlatform(url);
    let title = url;
    let image = getImageForPlatform(platform);
    let telegramChannel = null;

    if (platform === "YouTube") {
      try {
        const response = await fetch(`https://noembed.com/embed?url=${encodeURIComponent(url)}`);
        const data = await response.json();
        if (data.title) title = data.title;
        if (data.thumbnail_url) image = data.thumbnail_url;
      } catch (e) {
        console.warn("فشل استخراج العنوان تلقائيًا");
      }
    } else if (platform === "Telegram") {
      telegramChannel = extractTelegramChannel(url);
      title = telegramChannel ? `قناة Telegram: ${telegramChannel}` : url;
      // لا يمكن جلب صورة القناة الحقيقية بسهولة من جانب العميل بسبب CORS
      // لذلك نستخدم أيقونة تيليجرام الافتراضية
      image = getImageForPlatform(platform);
    }

    const date = new Date().toLocaleString("ar-EG", { dateStyle: "short", timeStyle: "short" });

    createCard(url, platform, title, image, date);
    saveToStorage(url, platform, title, image, date);
  }

  function extractTelegramChannel(url) {
    const match = url.match(/t\.me\/([a-zA-Z0-9_]+)/);
    return match && match[1] ? match[1] : null;
  }

  function createCard(url, platform, title, image, date) {
    const container = document.getElementById("cardsContainer");

    if (Array.from(container.children).some(card => card.querySelector("a").href === url)) return;

    const card = document.createElement("div");
    card.className = "card";
    card.dataset.platform = platform;

    card.innerHTML = `
      <button class="delete-btn" title="حذف" onclick="deleteCard(this)">×</button>
      <img src="${image}" alt="${platform}" loading="lazy" />
      <div class="platform">${platform}</div>
      <h3 title="${title}">${title.length > 45 ? title.slice(0, 42) + '...' : title}</h3>
      <p><a href="${url}" target="_blank" rel="noopener">${url}</a></p>
      <div class="date-added">تم الإضافة: ${date}</div>
    `;

    container.appendChild(card);
  }

  function deleteCard(button) {
    const card = button.parentElement;
    card.remove();
    updateStorage();
  }

  function saveToStorage(url, platform, title, image, date) {
    const existing = JSON.parse(localStorage.getItem(storageKey)) || [];
    if (!existing.some(item => item.url === url)) {
      existing.push({ url, platform, title, image, date });
      localStorage.setItem(storageKey, JSON.stringify(existing));
    }
  }

  function updateStorage() {
    const cards = document.querySelectorAll(".card");
    const newData = Array.from(cards).map(card => {
      return {
        url: card.querySelector("a").href,
        platform: card.dataset.platform,
        title: card.querySelector("h3").innerText,
        image: card.querySelector("img").src,
        date: card.querySelector(".date-added").innerText.replace('تم الإضافة: ', '')
      };
    });
    localStorage.setItem(storageKey, JSON.stringify(newData));
  }

  function filterCards() {
    const filter = document.getElementById("filterPlatform").value;
    const cards = document.querySelectorAll(".card");
    cards.forEach(card => {
      if (filter === "all" || card.dataset.platform === filter) {
        card.style.display = "flex";
      } else {
        card.style.display = "none";
      }
    });
  }

  function exportCSV() {
    const cards = document.querySelectorAll(".card");
    if(cards.length === 0) {
      alert("لا توجد روابط لتصديرها!");
      return;
    }
    const rows = [
      ["المنصة", "العنوان", "الرابط", "تاريخ الإضافة"]
    ];
    cards.forEach(card => {
      const platform = card.dataset.platform;
      const title = card.querySelector("h3").innerText;
      const url = card.querySelector("a").href;
      const date = card.querySelector(".date-added").innerText.replace('تم الإضافة: ', '');
      rows.push([platform, title, url, date]);
    });

    let csvContent = "data:text/csv;charset=utf-8," 
      + rows.map(e => e.map(a => `"${a.replace(/"/g, '""')}"`).join(",")).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "links_export.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
</script>

</body>
</html>
