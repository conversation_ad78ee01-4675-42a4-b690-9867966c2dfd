import type { Metadata } from "next";
import { Cairo } from "next/font/google";
import { Notifications } from "@/components/Notifications";
import "./globals.css";

const cairo = Cairo({
  subsets: ["arabic", "latin"],
  variable: "--font-cairo",
  weight: ["200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "عارض الروابط الذكي | Smart Links Viewer",
  description: "إدارة وتنظيم روابطك بذكاء مع دعم أكثر من 20 منصة",
  keywords: ["روابط", "إدارة", "تنظيم", "يوتيوب", "تلجرام", "تويتر", "انستجرام"],
  authors: [{ name: "Smart Links Viewer" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
  openGraph: {
    title: "عارض الروابط الذكي",
    description: "إدارة وتنظيم روابطك بذكاء",
    type: "website",
    locale: "ar_SA",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl" className={cairo.variable}>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className="font-sans antialiased bg-background text-foreground">
        <div className="relative min-h-screen">
          {children}
          <Notifications />
        </div>
      </body>
    </html>
  );
}
