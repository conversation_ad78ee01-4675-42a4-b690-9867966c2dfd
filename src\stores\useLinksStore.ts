import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { LinkData, Collection, FilterOptions, AppSettings, Analytics, NotificationData, ViewMode } from '@/types';

interface LinksStore {
  // State
  links: LinkData[];
  collections: Collection[];
  filteredLinks: LinkData[];
  selectedLinks: string[];
  currentFilter: FilterOptions;
  settings: AppSettings;
  analytics: Analytics;
  notifications: NotificationData[];
  viewMode: ViewMode;
  isLoading: boolean;
  searchQuery: string;

  // Actions
  addLink: (link: Omit<LinkData, 'id' | 'dateAdded' | 'clickCount' | 'lastAccessed'>) => void;
  updateLink: (id: string, updates: Partial<LinkData>) => void;
  deleteLink: (id: string) => void;
  deleteMultipleLinks: (ids: string[]) => void;
  toggleBookmark: (id: string) => void;
  incrementClickCount: (id: string) => void;
  
  // Collections
  createCollection: (collection: Omit<Collection, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateCollection: (id: string, updates: Partial<Collection>) => void;
  deleteCollection: (id: string) => void;
  addLinkToCollection: (linkId: string, collectionId: string) => void;
  removeLinkFromCollection: (linkId: string, collectionId: string) => void;

  // Filtering & Search
  setFilter: (filter: FilterOptions) => void;
  setSearchQuery: (query: string) => void;
  applyFilters: () => void;
  clearFilters: () => void;

  // Selection
  selectLink: (id: string) => void;
  selectMultipleLinks: (ids: string[]) => void;
  clearSelection: () => void;
  toggleLinkSelection: (id: string) => void;

  // Settings
  updateSettings: (settings: Partial<AppSettings>) => void;
  
  // View
  setViewMode: (mode: ViewMode) => void;
  
  // Notifications
  addNotification: (notification: Omit<NotificationData, 'id' | 'timestamp' | 'isRead'>) => void;
  markNotificationAsRead: (id: string) => void;
  clearNotifications: () => void;

  // Analytics
  updateAnalytics: () => void;
  
  // Import/Export
  exportData: () => string;
  importData: (data: string) => { success: boolean; message: string };
  
  // Utility
  setLoading: (loading: boolean) => void;
}

const defaultSettings: AppSettings = {
  theme: 'system',
  language: 'ar',
  defaultView: 'grid',
  itemsPerPage: 20,
  autoExtractMetadata: true,
  showNotifications: true,
  exportFormat: 'json',
  backupEnabled: false,
  syncEnabled: false,
};

const defaultViewMode: ViewMode = {
  type: 'grid',
  columns: 3,
  showImages: true,
  showMetadata: true,
  compactMode: false,
};

const defaultAnalytics: Analytics = {
  totalLinks: 0,
  totalClicks: 0,
  platformDistribution: {} as Record<any, number>,
  dailyActivity: [],
  topPlatforms: [],
  recentActivity: [],
};

export const useLinksStore = create<LinksStore>()(
  persist(
    (set, get) => ({
      // Initial State
      links: [],
      collections: [],
      filteredLinks: [],
      selectedLinks: [],
      currentFilter: {},
      settings: defaultSettings,
      analytics: defaultAnalytics,
      notifications: [],
      viewMode: defaultViewMode,
      isLoading: false,
      searchQuery: '',

      // Actions
      addLink: (linkData) => {
        const newLink: LinkData = {
          ...linkData,
          id: crypto.randomUUID(),
          dateAdded: new Date().toISOString(),
          clickCount: 0,
          isBookmarked: false,
          tags: linkData.tags || [],
        };

        set((state) => ({
          links: [newLink, ...state.links],
        }));

        get().applyFilters();
        get().updateAnalytics();
      },

      updateLink: (id, updates) => {
        set((state) => ({
          links: state.links.map((link) =>
            link.id === id ? { ...link, ...updates } : link
          ),
        }));
        get().applyFilters();
      },

      deleteLink: (id) => {
        set((state) => ({
          links: state.links.filter((link) => link.id !== id),
          selectedLinks: state.selectedLinks.filter((linkId) => linkId !== id),
        }));
        get().applyFilters();
        get().updateAnalytics();
      },

      deleteMultipleLinks: (ids) => {
        set((state) => ({
          links: state.links.filter((link) => !ids.includes(link.id)),
          selectedLinks: [],
        }));
        get().applyFilters();
        get().updateAnalytics();
      },

      toggleBookmark: (id) => {
        set((state) => ({
          links: state.links.map((link) =>
            link.id === id ? { ...link, isBookmarked: !link.isBookmarked } : link
          ),
        }));
        get().applyFilters();
      },

      incrementClickCount: (id) => {
        set((state) => ({
          links: state.links.map((link) =>
            link.id === id 
              ? { ...link, clickCount: link.clickCount + 1, lastAccessed: new Date().toISOString() }
              : link
          ),
        }));
        get().updateAnalytics();
      },

      // Collections
      createCollection: (collectionData) => {
        const newCollection: Collection = {
          ...collectionData,
          id: crypto.randomUUID(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          linkIds: [],
        };

        set((state) => ({
          collections: [...state.collections, newCollection],
        }));
      },

      updateCollection: (id, updates) => {
        set((state) => ({
          collections: state.collections.map((collection) =>
            collection.id === id 
              ? { ...collection, ...updates, updatedAt: new Date().toISOString() }
              : collection
          ),
        }));
      },

      deleteCollection: (id) => {
        set((state) => ({
          collections: state.collections.filter((collection) => collection.id !== id),
        }));
      },

      addLinkToCollection: (linkId, collectionId) => {
        set((state) => ({
          collections: state.collections.map((collection) =>
            collection.id === collectionId
              ? { 
                  ...collection, 
                  linkIds: [...collection.linkIds, linkId],
                  updatedAt: new Date().toISOString()
                }
              : collection
          ),
        }));
      },

      removeLinkFromCollection: (linkId, collectionId) => {
        set((state) => ({
          collections: state.collections.map((collection) =>
            collection.id === collectionId
              ? { 
                  ...collection, 
                  linkIds: collection.linkIds.filter(id => id !== linkId),
                  updatedAt: new Date().toISOString()
                }
              : collection
          ),
        }));
      },

      // Filtering & Search
      setFilter: (filter) => {
        set({ currentFilter: filter });
        get().applyFilters();
      },

      setSearchQuery: (query) => {
        set({ searchQuery: query });
        get().applyFilters();
      },

      applyFilters: () => {
        const { links, currentFilter, searchQuery } = get();
        let filtered = [...links];

        // Apply search query
        if (searchQuery) {
          const query = searchQuery.toLowerCase();
          filtered = filtered.filter(link =>
            link.title.toLowerCase().includes(query) ||
            link.url.toLowerCase().includes(query) ||
            link.description?.toLowerCase().includes(query) ||
            link.tags.some(tag => tag.toLowerCase().includes(query))
          );
        }

        // Apply platform filter
        if (currentFilter.platform && currentFilter.platform !== 'all') {
          filtered = filtered.filter(link => link.platform === currentFilter.platform);
        }

        // Apply bookmark filter
        if (currentFilter.isBookmarked !== undefined) {
          filtered = filtered.filter(link => link.isBookmarked === currentFilter.isBookmarked);
        }

        // Apply date range filter
        if (currentFilter.dateRange) {
          filtered = filtered.filter(link => {
            const linkDate = new Date(link.dateAdded);
            return linkDate >= currentFilter.dateRange!.start && linkDate <= currentFilter.dateRange!.end;
          });
        }

        // Apply tags filter
        if (currentFilter.tags && currentFilter.tags.length > 0) {
          filtered = filtered.filter(link =>
            currentFilter.tags!.some(tag => link.tags.includes(tag))
          );
        }

        // Apply sorting
        if (currentFilter.sortBy) {
          filtered.sort((a, b) => {
            const aValue = a[currentFilter.sortBy!];
            const bValue = b[currentFilter.sortBy!];
            const order = currentFilter.sortOrder === 'desc' ? -1 : 1;
            
            if (aValue < bValue) return -1 * order;
            if (aValue > bValue) return 1 * order;
            return 0;
          });
        }

        set({ filteredLinks: filtered });
      },

      clearFilters: () => {
        set({ 
          currentFilter: {},
          searchQuery: '',
          filteredLinks: get().links 
        });
      },

      // Selection
      selectLink: (id) => {
        set({ selectedLinks: [id] });
      },

      selectMultipleLinks: (ids) => {
        set({ selectedLinks: ids });
      },

      clearSelection: () => {
        set({ selectedLinks: [] });
      },

      toggleLinkSelection: (id) => {
        set((state) => ({
          selectedLinks: state.selectedLinks.includes(id)
            ? state.selectedLinks.filter(linkId => linkId !== id)
            : [...state.selectedLinks, id]
        }));
      },

      // Settings
      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings }
        }));
      },

      // View
      setViewMode: (mode) => {
        set({ viewMode: mode });
      },

      // Notifications
      addNotification: (notificationData) => {
        const notification: NotificationData = {
          ...notificationData,
          id: crypto.randomUUID(),
          timestamp: new Date().toISOString(),
          isRead: false,
        };

        set((state) => ({
          notifications: [notification, ...state.notifications.slice(0, 49)] // Keep only last 50
        }));
      },

      markNotificationAsRead: (id) => {
        set((state) => ({
          notifications: state.notifications.map(notification =>
            notification.id === id ? { ...notification, isRead: true } : notification
          )
        }));
      },

      clearNotifications: () => {
        set({ notifications: [] });
      },

      // Analytics
      updateAnalytics: () => {
        const { links } = get();
        
        const analytics: Analytics = {
          totalLinks: links.length,
          totalClicks: links.reduce((sum, link) => sum + link.clickCount, 0),
          platformDistribution: links.reduce((acc, link) => {
            acc[link.platform] = (acc[link.platform] || 0) + 1;
            return acc;
          }, {} as Record<any, number>),
          dailyActivity: [], // This would be calculated based on dateAdded
          topPlatforms: [], // This would be calculated from platformDistribution
          recentActivity: [], // This would track recent actions
        };

        set({ analytics });
      },

      // Import/Export
      exportData: () => {
        const { links, collections, settings } = get();
        return JSON.stringify({
          links,
          collections,
          settings,
          exportDate: new Date().toISOString(),
          version: '1.0.0'
        });
      },

      importData: (data) => {
        try {
          const parsed = JSON.parse(data);
          set({
            links: parsed.links || [],
            collections: parsed.collections || [],
            settings: { ...defaultSettings, ...parsed.settings }
          });
          get().applyFilters();
          get().updateAnalytics();
          return { success: true, message: 'تم استيراد البيانات بنجاح' };
        } catch (error) {
          return { success: false, message: 'فشل في استيراد البيانات' };
        }
      },

      // Utility
      setLoading: (loading) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'modern-links-storage',
      partialize: (state) => ({
        links: state.links,
        collections: state.collections,
        settings: state.settings,
      }),
    }
  )
);
