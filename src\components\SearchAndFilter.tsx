'use client';

import React, { useState, useEffect } from 'react';
import { useLinksStore } from '@/stores/useLinksStore';
import { Platform } from '@/types';
import { 
  Search, 
  Filter, 
  X, 
  Calendar,
  Tag,
  Bookmark,
  SortAsc,
  SortDesc,
  Grid3X3,
  List,
  LayoutGrid,
  Settings
} from 'lucide-react';

const PLATFORMS: (Platform | 'all')[] = [
  'all',
  'YouTube',
  'Telegram',
  'Twitter',
  'Instagram',
  'TikTok',
  'LinkedIn',
  'Reddit',
  'Facebook',
  'WhatsApp',
  'Discord',
  'Twitch',
  'GitHub',
  'Medium',
  'Pinterest',
  'Snapchat',
  'Vimeo',
  'Dailymotion',
  'SoundCloud',
  'Spotify',
  'رابط عام'
];

const SORT_OPTIONS = [
  { value: 'dateAdded', label: 'تاريخ الإضافة' },
  { value: 'title', label: 'العنوان' },
  { value: 'platform', label: 'المنصة' },
  { value: 'clickCount', label: 'عدد النقرات' },
  { value: 'lastAccessed', label: 'آخر وصول' },
] as const;

export function SearchAndFilter() {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  
  const {
    searchQuery,
    setSearchQuery,
    currentFilter,
    setFilter,
    clearFilters,
    links,
    filteredLinks,
    viewMode,
    setViewMode
  } = useLinksStore();

  // Get all unique tags from links
  const allTags = Array.from(
    new Set(links.flatMap(link => link.tags))
  ).sort();

  const handlePlatformChange = (platform: Platform | 'all') => {
    setFilter({
      ...currentFilter,
      platform: platform === 'all' ? undefined : platform
    });
  };

  const handleSortChange = (sortBy: string, sortOrder: 'asc' | 'desc') => {
    setFilter({
      ...currentFilter,
      sortBy: sortBy as any,
      sortOrder
    });
  };

  const handleBookmarkFilter = (isBookmarked?: boolean) => {
    setFilter({
      ...currentFilter,
      isBookmarked
    });
  };

  const handleDateRangeChange = () => {
    if (dateRange.start && dateRange.end) {
      setFilter({
        ...currentFilter,
        dateRange: {
          start: new Date(dateRange.start),
          end: new Date(dateRange.end)
        }
      });
    } else {
      const { dateRange: _, ...filterWithoutDate } = currentFilter;
      setFilter(filterWithoutDate);
    }
  };

  const handleTagsChange = (tags: string[]) => {
    setSelectedTags(tags);
    setFilter({
      ...currentFilter,
      tags: tags.length > 0 ? tags : undefined
    });
  };

  const toggleTag = (tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];
    handleTagsChange(newTags);
  };

  const handleClearFilters = () => {
    clearFilters();
    setDateRange({ start: '', end: '' });
    setSelectedTags([]);
    setShowAdvancedFilters(false);
  };

  const hasActiveFilters = 
    currentFilter.platform ||
    currentFilter.isBookmarked !== undefined ||
    currentFilter.dateRange ||
    currentFilter.tags?.length ||
    searchQuery;

  useEffect(() => {
    handleDateRangeChange();
  }, [dateRange]);

  return (
    <div className="space-y-4">
      {/* Main Search and Quick Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search Input */}
        <div className="relative flex-1">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={20} />
          <input
            type="text"
            placeholder="البحث في الروابط..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-4 pr-12 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-foreground text-right"
            dir="rtl"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
            >
              <X size={16} />
            </button>
          )}
        </div>

        {/* Platform Filter */}
        <select
          value={currentFilter.platform || 'all'}
          onChange={(e) => handlePlatformChange(e.target.value as Platform | 'all')}
          className="px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-background text-foreground min-w-[150px]"
        >
          <option value="all">جميع المنصات</option>
          {PLATFORMS.slice(1).map(platform => (
            <option key={platform} value={platform}>
              {platform}
            </option>
          ))}
        </select>

        {/* View Mode Toggle */}
        <div className="flex items-center border border-border rounded-lg overflow-hidden">
          <button
            onClick={() => setViewMode({ ...viewMode, type: 'grid' })}
            className={`p-3 transition-colors ${
              viewMode.type === 'grid' 
                ? 'bg-primary text-primary-foreground' 
                : 'hover:bg-muted'
            }`}
            title="عرض شبكي"
          >
            <Grid3X3 size={20} />
          </button>
          <button
            onClick={() => setViewMode({ ...viewMode, type: 'list' })}
            className={`p-3 transition-colors ${
              viewMode.type === 'list' 
                ? 'bg-primary text-primary-foreground' 
                : 'hover:bg-muted'
            }`}
            title="عرض قائمة"
          >
            <List size={20} />
          </button>
        </div>

        {/* Advanced Filters Toggle */}
        <button
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className={`inline-flex items-center gap-2 px-4 py-3 border rounded-lg transition-colors ${
            showAdvancedFilters || hasActiveFilters
              ? 'border-primary bg-primary/10 text-primary'
              : 'border-border hover:bg-muted'
          }`}
        >
          <Filter size={20} />
          فلاتر
          {hasActiveFilters && (
            <span className="w-2 h-2 bg-primary rounded-full"></span>
          )}
        </button>
      </div>

      {/* Quick Filter Buttons */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => handleBookmarkFilter(true)}
          className={`inline-flex items-center gap-2 px-3 py-2 rounded-full text-sm transition-colors ${
            currentFilter.isBookmarked === true
              ? 'bg-yellow-100 text-yellow-800 border border-yellow-200'
              : 'bg-muted hover:bg-muted/80'
          }`}
        >
          <Bookmark size={14} />
          المفضلة
        </button>

        <button
          onClick={() => handleSortChange('dateAdded', 'desc')}
          className={`inline-flex items-center gap-2 px-3 py-2 rounded-full text-sm transition-colors ${
            currentFilter.sortBy === 'dateAdded' && currentFilter.sortOrder === 'desc'
              ? 'bg-primary/10 text-primary border border-primary/20'
              : 'bg-muted hover:bg-muted/80'
          }`}
        >
          <Calendar size={14} />
          الأحدث
        </button>

        <button
          onClick={() => handleSortChange('clickCount', 'desc')}
          className={`inline-flex items-center gap-2 px-3 py-2 rounded-full text-sm transition-colors ${
            currentFilter.sortBy === 'clickCount' && currentFilter.sortOrder === 'desc'
              ? 'bg-primary/10 text-primary border border-primary/20'
              : 'bg-muted hover:bg-muted/80'
          }`}
        >
          <SortDesc size={14} />
          الأكثر زيارة
        </button>

        {hasActiveFilters && (
          <button
            onClick={handleClearFilters}
            className="inline-flex items-center gap-2 px-3 py-2 rounded-full text-sm bg-destructive/10 text-destructive hover:bg-destructive/20 transition-colors"
          >
            <X size={14} />
            مسح الفلاتر
          </button>
        )}
      </div>

      {/* Advanced Filters Panel */}
      {showAdvancedFilters && (
        <div className="bg-card border rounded-lg p-6 space-y-6 animate-slide-up">
          <h3 className="font-semibold text-foreground flex items-center gap-2">
            <Settings size={20} />
            فلاتر متقدمة
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Date Range */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                نطاق التاريخ
              </label>
              <div className="grid grid-cols-2 gap-2">
                <input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                  className="px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-background text-foreground"
                />
                <input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                  className="px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-background text-foreground"
                />
              </div>
            </div>

            {/* Sort Options */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                ترتيب حسب
              </label>
              <div className="flex gap-2">
                <select
                  value={currentFilter.sortBy || 'dateAdded'}
                  onChange={(e) => handleSortChange(e.target.value, currentFilter.sortOrder || 'desc')}
                  className="flex-1 px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-background text-foreground"
                >
                  {SORT_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <button
                  onClick={() => handleSortChange(
                    currentFilter.sortBy || 'dateAdded',
                    currentFilter.sortOrder === 'asc' ? 'desc' : 'asc'
                  )}
                  className="px-3 py-2 border border-border rounded-lg hover:bg-muted transition-colors"
                  title={currentFilter.sortOrder === 'asc' ? 'تصاعدي' : 'تنازلي'}
                >
                  {currentFilter.sortOrder === 'asc' ? <SortAsc size={16} /> : <SortDesc size={16} />}
                </button>
              </div>
            </div>
          </div>

          {/* Tags Filter */}
          {allTags.length > 0 && (
            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground flex items-center gap-2">
                <Tag size={16} />
                العلامات
              </label>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                {allTags.map(tag => (
                  <button
                    key={tag}
                    onClick={() => toggleTag(tag)}
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm transition-colors ${
                      selectedTags.includes(tag)
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted hover:bg-muted/80'
                    }`}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <div>
          عرض {filteredLinks.length} من أصل {links.length} رابط
        </div>
        
        {hasActiveFilters && (
          <div className="flex items-center gap-2">
            <span>فلاتر نشطة:</span>
            {currentFilter.platform && (
              <span className="px-2 py-1 bg-primary/10 text-primary rounded text-xs">
                {currentFilter.platform}
              </span>
            )}
            {currentFilter.isBookmarked && (
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">
                مفضلة
              </span>
            )}
            {currentFilter.tags?.length && (
              <span className="px-2 py-1 bg-secondary text-secondary-foreground rounded text-xs">
                {currentFilter.tags.length} علامة
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
