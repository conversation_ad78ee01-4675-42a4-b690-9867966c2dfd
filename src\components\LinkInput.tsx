'use client';

import React, { useState, useRef } from 'react';
import { useLinksStore } from '@/stores/useLinksStore';
import { extractLinkData, validateUrl, normalizeUrl, generateTags } from '@/lib/linkExtractor';
import { 
  Plus, 
  Link as LinkIcon, 
  Upload, 
  Loader2, 
  AlertCircle,
  CheckCircle,
  X,
  FileText,
  Globe
} from 'lucide-react';

export function LinkInput() {
  const [input, setInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [processingResults, setProcessingResults] = useState<{
    success: number;
    failed: number;
    duplicates: number;
  }>({ success: 0, failed: 0, duplicates: 0 });
  const [showResults, setShowResults] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { addLink, links, addNotification, setLoading } = useLinksStore();

  const processLinks = async () => {
    if (!input.trim()) return;

    setIsProcessing(true);
    setLoading(true);
    
    const urls = input
      .split(/[\n,]/)
      .map(url => url.trim())
      .filter(url => url.length > 0);

    let success = 0;
    let failed = 0;
    let duplicates = 0;

    for (const url of urls) {
      try {
        if (!validateUrl(url)) {
          failed++;
          continue;
        }

        const normalizedUrl = normalizeUrl(url);
        
        // Check for duplicates
        if (links.some(link => link.url === normalizedUrl)) {
          duplicates++;
          continue;
        }

        // Extract link data
        const linkData = await extractLinkData(normalizedUrl);
        const tags = generateTags(linkData);

        addLink({
          url: normalizedUrl,
          title: linkData.title,
          description: linkData.description,
          image: linkData.image,
          platform: linkData.platform,
          tags,
          metadata: linkData.metadata,
        });

        success++;
      } catch (error) {
        console.error('فشل في معالجة الرابط:', url, error);
        failed++;
      }
    }

    setProcessingResults({ success, failed, duplicates });
    setShowResults(true);
    setInput('');
    setIsProcessing(false);
    setLoading(false);

    // Show notification
    if (success > 0) {
      addNotification({
        type: 'success',
        title: 'تم إضافة الروابط',
        message: `تم إضافة ${success} رابط بنجاح${failed > 0 ? ` وفشل في ${failed}` : ''}${duplicates > 0 ? ` و${duplicates} مكرر` : ''}`
      });
    } else {
      addNotification({
        type: 'error',
        title: 'فشل في إضافة الروابط',
        message: 'لم يتم إضافة أي رابط'
      });
    }

    // Hide results after 5 seconds
    setTimeout(() => setShowResults(false), 5000);
  };

  const handleFileUpload = async (file: File) => {
    if (!file.type.includes('text')) {
      addNotification({
        type: 'error',
        title: 'نوع ملف غير مدعوم',
        message: 'يرجى رفع ملف نصي فقط'
      });
      return;
    }

    try {
      const text = await file.text();
      setInput(prev => prev + (prev ? '\n' : '') + text);
      addNotification({
        type: 'success',
        title: 'تم رفع الملف',
        message: 'تم تحميل الروابط من الملف'
      });
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'فشل في قراءة الملف',
        message: 'لم يتم قراءة الملف بنجاح'
      });
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      setInput(prev => prev + (prev ? '\n' : '') + text);
      addNotification({
        type: 'success',
        title: 'تم اللصق',
        message: 'تم لصق المحتوى من الحافظة'
      });
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'فشل في اللصق',
        message: 'لم يتم الوصول للحافظة'
      });
    }
  };

  return (
    <div className="space-y-4">
      {/* Main Input Area */}
      <div 
        className={`
          relative border-2 border-dashed rounded-xl p-6 transition-all duration-200
          ${dragActive 
            ? 'border-primary bg-primary/5' 
            : 'border-border hover:border-primary/50'
          }
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div className="space-y-4">
          {/* Header */}
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 mx-auto mb-3 bg-primary/10 rounded-full">
              <LinkIcon className="w-6 h-6 text-primary" />
            </div>
            <h3 className="text-lg font-semibold text-foreground">إضافة روابط جديدة</h3>
            <p className="text-sm text-muted-foreground mt-1">
              الصق رابطًا أو عدة روابط، أو اسحب ملف نصي هنا
            </p>
          </div>

          {/* Textarea */}
          <div className="relative">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="الصق الروابط هنا... (رابط واحد في كل سطر أو مفصولة بفواصل)"
              className="w-full h-32 p-4 border border-border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-foreground placeholder:text-muted-foreground text-right"
              dir="rtl"
              disabled={isProcessing}
            />
            
            {/* Character Count */}
            <div className="absolute bottom-2 left-2 text-xs text-muted-foreground">
              {input.length} حرف
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between gap-3">
            <div className="flex items-center gap-2">
              {/* File Upload */}
              <button
                onClick={() => fileInputRef.current?.click()}
                disabled={isProcessing}
                className="inline-flex items-center gap-2 px-3 py-2 text-sm border border-border rounded-lg hover:bg-muted transition-colors disabled:opacity-50"
              >
                <Upload size={16} />
                رفع ملف
              </button>

              {/* Paste from Clipboard */}
              <button
                onClick={handlePaste}
                disabled={isProcessing}
                className="inline-flex items-center gap-2 px-3 py-2 text-sm border border-border rounded-lg hover:bg-muted transition-colors disabled:opacity-50"
              >
                <FileText size={16} />
                لصق
              </button>

              {/* Clear */}
              {input && (
                <button
                  onClick={() => setInput('')}
                  disabled={isProcessing}
                  className="inline-flex items-center gap-2 px-3 py-2 text-sm text-destructive border border-destructive/20 rounded-lg hover:bg-destructive/10 transition-colors disabled:opacity-50"
                >
                  <X size={16} />
                  مسح
                </button>
              )}
            </div>

            {/* Process Button */}
            <button
              onClick={processLinks}
              disabled={!input.trim() || isProcessing}
              className="inline-flex items-center gap-2 px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                <>
                  <Plus size={16} />
                  تحليل الروابط
                </>
              )}
            </button>
          </div>
        </div>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept=".txt,.csv"
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) handleFileUpload(file);
          }}
          className="hidden"
        />

        {/* Drag Overlay */}
        {dragActive && (
          <div className="absolute inset-0 bg-primary/10 border-2 border-primary border-dashed rounded-xl flex items-center justify-center">
            <div className="text-center">
              <Upload className="w-8 h-8 text-primary mx-auto mb-2" />
              <p className="text-primary font-medium">اسحب الملف هنا</p>
            </div>
          </div>
        )}
      </div>

      {/* Processing Results */}
      {showResults && (
        <div className="bg-card border rounded-lg p-4 animate-slide-up">
          <h4 className="font-medium text-foreground mb-3 flex items-center gap-2">
            <CheckCircle size={16} className="text-green-500" />
            نتائج المعالجة
          </h4>
          
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">{processingResults.success}</div>
              <div className="text-muted-foreground">تم إضافتها</div>
            </div>
            
            {processingResults.failed > 0 && (
              <div className="text-center">
                <div className="text-2xl font-bold text-red-500">{processingResults.failed}</div>
                <div className="text-muted-foreground">فشلت</div>
              </div>
            )}
            
            {processingResults.duplicates > 0 && (
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-500">{processingResults.duplicates}</div>
                <div className="text-muted-foreground">مكررة</div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quick Tips */}
      <div className="bg-muted/50 rounded-lg p-4">
        <h4 className="font-medium text-foreground mb-2 flex items-center gap-2">
          <Globe size={16} />
          نصائح سريعة
        </h4>
        <ul className="text-sm text-muted-foreground space-y-1 text-right">
          <li>• يمكنك إضافة عدة روابط في نفس الوقت</li>
          <li>• يتم استخراج العناوين والصور تلقائيًا</li>
          <li>• الروابط المكررة يتم تجاهلها</li>
          <li>• يمكنك رفع ملف نصي يحتوي على روابط</li>
        </ul>
      </div>
    </div>
  );
}
