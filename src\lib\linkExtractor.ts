import { Platform, LinkMetadata } from '@/types';

export interface ExtractedLinkData {
  title: string;
  description?: string;
  image: string;
  platform: Platform;
  metadata?: LinkMetadata;
}

// Platform detection patterns
const PLATFORM_PATTERNS: Record<Platform, RegExp[]> = {
  'YouTube': [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)/i,
  ],
  'Telegram': [
    /t\.me\//i,
  ],
  'Twitter': [
    /(?:twitter\.com|x\.com)\//i,
  ],
  'Instagram': [
    /instagram\.com\//i,
  ],
  'TikTok': [
    /tiktok\.com\//i,
  ],
  'LinkedIn': [
    /linkedin\.com\//i,
  ],
  'Reddit': [
    /reddit\.com\//i,
  ],
  'Facebook': [
    /facebook\.com\//i,
  ],
  'WhatsApp': [
    /(?:wa\.me|whatsapp\.com)\//i,
  ],
  'Discord': [
    /discord\.(?:gg|com)\//i,
  ],
  'Twitch': [
    /twitch\.tv\//i,
  ],
  'GitHub': [
    /github\.com\//i,
  ],
  'Medium': [
    /medium\.com\//i,
  ],
  'Pinterest': [
    /pinterest\.com\//i,
  ],
  'Snapchat': [
    /snapchat\.com\//i,
  ],
  'Vimeo': [
    /vimeo\.com\//i,
  ],
  'Dailymotion': [
    /dailymotion\.com\//i,
  ],
  'SoundCloud': [
    /soundcloud\.com\//i,
  ],
  'Spotify': [
    /(?:open\.spotify\.com|spotify\.com)\//i,
  ],
  'رابط عام': [],
};

// Default images for platforms
const PLATFORM_IMAGES: Record<Platform, string> = {
  'YouTube': 'https://img.icons8.com/color/480/youtube-play.png',
  'Telegram': 'https://img.icons8.com/color/480/telegram-app.png',
  'Twitter': 'https://img.icons8.com/color/480/twitter.png',
  'Instagram': 'https://img.icons8.com/color/480/instagram-new.png',
  'TikTok': 'https://img.icons8.com/color/480/tiktok.png',
  'LinkedIn': 'https://img.icons8.com/color/480/linkedin.png',
  'Reddit': 'https://img.icons8.com/color/480/reddit.png',
  'Facebook': 'https://img.icons8.com/color/480/facebook.png',
  'WhatsApp': 'https://img.icons8.com/color/480/whatsapp.png',
  'Discord': 'https://img.icons8.com/color/480/discord-logo.png',
  'Twitch': 'https://img.icons8.com/color/480/twitch.png',
  'GitHub': 'https://img.icons8.com/color/480/github.png',
  'Medium': 'https://img.icons8.com/color/480/medium-logo.png',
  'Pinterest': 'https://img.icons8.com/color/480/pinterest.png',
  'Snapchat': 'https://img.icons8.com/color/480/snapchat.png',
  'Vimeo': 'https://img.icons8.com/color/480/vimeo.png',
  'Dailymotion': 'https://img.icons8.com/color/480/dailymotion.png',
  'SoundCloud': 'https://img.icons8.com/color/480/soundcloud.png',
  'Spotify': 'https://img.icons8.com/color/480/spotify.png',
  'رابط عام': 'https://img.icons8.com/fluency/480/link.png',
};

export function detectPlatform(url: string): Platform {
  for (const [platform, patterns] of Object.entries(PLATFORM_PATTERNS)) {
    if (patterns.some(pattern => pattern.test(url))) {
      return platform as Platform;
    }
  }
  return 'رابط عام';
}

export function getPlatformImage(platform: Platform): string {
  return PLATFORM_IMAGES[platform];
}

export async function extractLinkData(url: string): Promise<ExtractedLinkData> {
  const platform = detectPlatform(url);
  let title = url;
  let description = '';
  let image = getPlatformImage(platform);
  let metadata: LinkMetadata = {};

  try {
    switch (platform) {
      case 'YouTube':
        const youtubeData = await extractYouTubeData(url);
        if (youtubeData) {
          title = youtubeData.title || title;
          description = youtubeData.description;
          image = youtubeData.thumbnail || image;
          metadata = youtubeData.metadata || {};
        }
        break;

      case 'Telegram':
        const telegramData = extractTelegramData(url);
        title = telegramData.title;
        metadata = telegramData.metadata;
        break;

      case 'Twitter':
        // Twitter API extraction would go here
        title = extractTwitterTitle(url);
        break;

      case 'Instagram':
        // Instagram extraction would go here
        title = extractInstagramTitle(url);
        break;

      default:
        // Try generic metadata extraction
        const genericData = await extractGenericMetadata(url);
        if (genericData) {
          title = genericData.title || title;
          description = genericData.description;
          image = genericData.image || image;
        }
        break;
    }
  } catch (error) {
    console.warn(`فشل في استخراج البيانات من ${platform}:`, error);
  }

  return {
    title,
    description,
    image,
    platform,
    metadata,
  };
}

async function extractYouTubeData(url: string) {
  try {
    // Try multiple APIs for YouTube data extraction
    const apis = [
      `https://noembed.com/embed?url=${encodeURIComponent(url)}`,
      `https://www.youtube.com/oembed?url=${encodeURIComponent(url)}&format=json`,
    ];

    for (const apiUrl of apis) {
      try {
        const response = await fetch(apiUrl);
        if (response.ok) {
          const data = await response.json();
          
          if (data.title) {
            return {
              title: data.title,
              description: data.description || '',
              thumbnail: data.thumbnail_url || data.thumbnail,
              metadata: {
                author: data.author_name || data.author,
                channelName: data.author_name || data.author,
                duration: data.duration,
                views: data.view_count,
                publishDate: data.upload_date,
              },
            };
          }
        }
      } catch (apiError) {
        console.warn('فشل في API:', apiUrl, apiError);
        continue;
      }
    }
  } catch (error) {
    console.warn('فشل في استخراج بيانات YouTube:', error);
  }
  return null;
}

function extractTelegramData(url: string) {
  const channelMatch = url.match(/t\.me\/([a-zA-Z0-9_]+)/);
  const channelName = channelMatch?.[1];
  
  return {
    title: channelName ? `قناة Telegram: ${channelName}` : 'رابط Telegram',
    metadata: {
      channelName: channelName || undefined,
    },
  };
}

function extractTwitterTitle(url: string): string {
  const tweetMatch = url.match(/(?:twitter\.com|x\.com)\/\w+\/status\/(\d+)/);
  if (tweetMatch) {
    return `تغريدة Twitter`;
  }
  
  const userMatch = url.match(/(?:twitter\.com|x\.com)\/(\w+)/);
  if (userMatch) {
    return `حساب Twitter: @${userMatch[1]}`;
  }
  
  return 'رابط Twitter';
}

function extractInstagramTitle(url: string): string {
  const postMatch = url.match(/instagram\.com\/p\/([a-zA-Z0-9_-]+)/);
  if (postMatch) {
    return `منشور Instagram`;
  }
  
  const userMatch = url.match(/instagram\.com\/([a-zA-Z0-9_.]+)/);
  if (userMatch) {
    return `حساب Instagram: @${userMatch[1]}`;
  }
  
  return 'رابط Instagram';
}

async function extractGenericMetadata(url: string) {
  try {
    // This would typically require a backend service due to CORS
    // For now, we'll return null and rely on platform-specific extraction
    return null;
  } catch (error) {
    console.warn('فشل في استخراج البيانات العامة:', error);
    return null;
  }
}

export function validateUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function normalizeUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    
    // Remove tracking parameters
    const trackingParams = [
      'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
      'fbclid', 'gclid', 'ref', 'source', 'campaign'
    ];
    
    trackingParams.forEach(param => {
      urlObj.searchParams.delete(param);
    });
    
    return urlObj.toString();
  } catch {
    return url;
  }
}

export function extractDomain(url: string): string {
  try {
    return new URL(url).hostname;
  } catch {
    return '';
  }
}

export function generateTags(linkData: ExtractedLinkData): string[] {
  const tags: string[] = [];
  
  // Add platform as tag
  tags.push(linkData.platform);
  
  // Add domain-based tags
  const domain = extractDomain(linkData.title);
  if (domain) {
    tags.push(domain);
  }
  
  // Add content-based tags (simple keyword extraction)
  const keywords = extractKeywords(linkData.title + ' ' + (linkData.description || ''));
  tags.push(...keywords);
  
  return [...new Set(tags)]; // Remove duplicates
}

function extractKeywords(text: string): string[] {
  // Simple keyword extraction - in a real app, you might use NLP
  const commonWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'التي', 'الذي'];
  const words = text.toLowerCase()
    .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\w\s]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 2 && !commonWords.includes(word));
  
  return words.slice(0, 5); // Return top 5 keywords
}
