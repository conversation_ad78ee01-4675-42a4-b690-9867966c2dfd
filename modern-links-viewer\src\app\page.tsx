'use client';

import React, { useState, useEffect } from 'react';
import { useLinksStore } from '@/stores/useLinksStore';
import { LinkCard } from '@/components/LinkCard';
import { LinkInput } from '@/components/LinkInput';
import { SearchAndFilter } from '@/components/SearchAndFilter';
import {
  Plus,
  Download,
  Upload,
  Settings,
  BarChart3,
  Bookmark,
  Link as LinkIcon,
  Sparkles,
  TrendingUp,
  Clock,
  Grid3X3
} from 'lucide-react';

export default function Home() {
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedLinks, setSelectedLinks] = useState<string[]>([]);

  const {
    filteredLinks,
    links,
    analytics,
    viewMode,
    isLoading,
    updateAnalytics,
    exportData,
    deleteMultipleLinks,
    addNotification
  } = useLinksStore();

  useEffect(() => {
    updateAnalytics();
  }, [links, updateAnalytics]);

  const handleSelectLink = (id: string) => {
    setSelectedLinks(prev =>
      prev.includes(id)
        ? prev.filter(linkId => linkId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedLinks.length === filteredLinks.length) {
      setSelectedLinks([]);
    } else {
      setSelectedLinks(filteredLinks.map(link => link.id));
    }
  };

  const handleDeleteSelected = () => {
    if (selectedLinks.length === 0) return;

    deleteMultipleLinks(selectedLinks);
    setSelectedLinks([]);
    addNotification({
      type: 'success',
      title: 'تم الحذف',
      message: `تم حذف ${selectedLinks.length} رابط`
    });
  };

  const handleExport = () => {
    try {
      const data = exportData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `links-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      addNotification({
        type: 'success',
        title: 'تم التصدير',
        message: 'تم تصدير البيانات بنجاح'
      });
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'فشل التصدير',
        message: 'حدث خطأ أثناء تصدير البيانات'
      });
    }
  };

  const recentLinks = links.slice(0, 5);
  const bookmarkedLinks = links.filter(link => link.isBookmarked);
  const topPlatforms = Object.entries(analytics.platformDistribution)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/70 rounded-xl flex items-center justify-center">
                <LinkIcon className="w-6 h-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">عارض الروابط الذكي</h1>
                <p className="text-sm text-muted-foreground">إدارة وتنظيم روابطك بذكاء</p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowAddForm(!showAddForm)}
                className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                <Plus size={20} />
                إضافة رابط
              </button>

              <button
                onClick={handleExport}
                className="inline-flex items-center gap-2 px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors"
                title="تصدير البيانات"
              >
                <Download size={20} />
              </button>

              <button
                className="inline-flex items-center gap-2 px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors"
                title="الإعدادات"
              >
                <Settings size={20} />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-card rounded-xl p-6 border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">إجمالي الروابط</p>
                <p className="text-3xl font-bold text-foreground">{analytics.totalLinks}</p>
              </div>
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                <LinkIcon className="w-6 h-6 text-primary" />
              </div>
            </div>
          </div>

          <div className="bg-card rounded-xl p-6 border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">إجمالي النقرات</p>
                <p className="text-3xl font-bold text-foreground">{analytics.totalClicks}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-card rounded-xl p-6 border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">المفضلة</p>
                <p className="text-3xl font-bold text-foreground">{bookmarkedLinks.length}</p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Bookmark className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
          </div>

          <div className="bg-card rounded-xl p-6 border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">المنصات</p>
                <p className="text-3xl font-bold text-foreground">{Object.keys(analytics.platformDistribution).length}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Grid3X3 className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Add Link Form */}
        {showAddForm && (
          <div className="mb-8 animate-slide-up">
            <div className="bg-card rounded-xl border p-6">
              <LinkInput />
            </div>
          </div>
        )}

        {/* Search and Filter */}
        <div className="mb-8">
          <SearchAndFilter />
        </div>

        {/* Bulk Actions */}
        {selectedLinks.length > 0 && (
          <div className="mb-6 p-4 bg-primary/10 border border-primary/20 rounded-lg animate-slide-up">
            <div className="flex items-center justify-between">
              <span className="text-primary font-medium">
                تم تحديد {selectedLinks.length} رابط
              </span>
              <div className="flex items-center gap-2">
                <button
                  onClick={handleSelectAll}
                  className="px-3 py-1 text-sm border border-primary/20 rounded hover:bg-primary/10 transition-colors"
                >
                  {selectedLinks.length === filteredLinks.length ? 'إلغاء تحديد الكل' : 'تحديد الكل'}
                </button>
                <button
                  onClick={handleDeleteSelected}
                  className="px-3 py-1 text-sm bg-destructive text-destructive-foreground rounded hover:bg-destructive/90 transition-colors"
                >
                  حذف المحدد
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Links Grid/List */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-muted-foreground">جاري التحميل...</p>
            </div>
          </div>
        ) : filteredLinks.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <LinkIcon className="w-12 h-12 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-2">لا توجد روابط</h3>
            <p className="text-muted-foreground mb-6">
              {links.length === 0
                ? 'ابدأ بإضافة أول رابط لك'
                : 'لا توجد روابط تطابق معايير البحث'
              }
            </p>
            {links.length === 0 && (
              <button
                onClick={() => setShowAddForm(true)}
                className="inline-flex items-center gap-2 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                <Plus size={20} />
                إضافة رابط جديد
              </button>
            )}
          </div>
        ) : (
          <div className={`
            ${viewMode.type === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
              : 'space-y-4'
            }
          `}>
            {filteredLinks.map((link) => (
              <LinkCard
                key={link.id}
                link={link}
                isSelected={selectedLinks.includes(link.id)}
                onSelect={handleSelectLink}
                viewMode={viewMode.type}
              />
            ))}
          </div>
        )}

        {/* Quick Access Sidebar */}
        {links.length > 0 && (
          <div className="fixed left-4 top-1/2 transform -translate-y-1/2 hidden xl:block">
            <div className="bg-card border rounded-xl p-4 w-64 space-y-4">
              <h3 className="font-semibold text-foreground flex items-center gap-2">
                <Clock size={16} />
                الروابط الأخيرة
              </h3>
              <div className="space-y-2">
                {recentLinks.map((link) => (
                  <div key={link.id} className="flex items-center gap-2 p-2 rounded hover:bg-muted cursor-pointer">
                    <img src={link.image} alt="" className="w-8 h-8 rounded object-cover" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{link.title}</p>
                      <p className="text-xs text-muted-foreground">{link.platform}</p>
                    </div>
                  </div>
                ))}
              </div>

              {topPlatforms.length > 0 && (
                <>
                  <hr className="border-border" />
                  <h3 className="font-semibold text-foreground flex items-center gap-2">
                    <BarChart3 size={16} />
                    أهم المنصات
                  </h3>
                  <div className="space-y-2">
                    {topPlatforms.map(([platform, count]) => (
                      <div key={platform} className="flex items-center justify-between">
                        <span className="text-sm">{platform}</span>
                        <span className="text-sm font-medium">{count}</span>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
