'use client';

import React, { useEffect } from 'react';
import { useLinksStore } from '@/stores/useLinksStore';
import { NotificationData } from '@/types';
import { 
  CheckCircle, 
  AlertCircle, 
  XCircle, 
  Info, 
  X 
} from 'lucide-react';

const NOTIFICATION_ICONS = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertCircle,
  info: Info,
};

const NOTIFICATION_STYLES = {
  success: 'bg-green-50 border-green-200 text-green-800',
  error: 'bg-red-50 border-red-200 text-red-800',
  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  info: 'bg-blue-50 border-blue-200 text-blue-800',
};

interface NotificationItemProps {
  notification: NotificationData;
  onDismiss: (id: string) => void;
}

function NotificationItem({ notification, onDismiss }: NotificationItemProps) {
  const Icon = NOTIFICATION_ICONS[notification.type];
  
  useEffect(() => {
    const timer = setTimeout(() => {
      onDismiss(notification.id);
    }, 5000);

    return () => clearTimeout(timer);
  }, [notification.id, onDismiss]);

  return (
    <div 
      className={`
        relative p-4 rounded-lg border shadow-lg animate-slide-up
        ${NOTIFICATION_STYLES[notification.type]}
      `}
      role="alert"
    >
      <div className="flex items-start gap-3">
        <Icon size={20} className="flex-shrink-0 mt-0.5" />
        
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-right">{notification.title}</h4>
          <p className="text-sm mt-1 text-right">{notification.message}</p>
          
          {notification.action && (
            <button
              onClick={notification.action.onClick}
              className="mt-2 text-sm font-medium underline hover:no-underline"
            >
              {notification.action.label}
            </button>
          )}
        </div>
        
        <button
          onClick={() => onDismiss(notification.id)}
          className="flex-shrink-0 p-1 rounded hover:bg-black/10 transition-colors"
          aria-label="إغلاق الإشعار"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  );
}

export function Notifications() {
  const { notifications, markNotificationAsRead } = useLinksStore();
  
  const unreadNotifications = notifications.filter(n => !n.isRead);

  const handleDismiss = (id: string) => {
    markNotificationAsRead(id);
  };

  if (unreadNotifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-3 max-w-sm w-full">
      {unreadNotifications.slice(0, 5).map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onDismiss={handleDismiss}
        />
      ))}
    </div>
  );
}
